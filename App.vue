<script setup>
  import { onLaunch, onShow, onError } from '@dcloudio/uni-app';
  import { ShoproInit } from './sheep';

  onLaunch(() => {
    // 隐藏原生导航栏 使用自定义底部导航
    uni.hideTabBar({
      fail: () => {},
    });

    // 加载Shopro底层依赖
    ShoproInit();
    uni.loadFontFace({
				global: true,
				family: 'Source Han Serif',
				source: 'url("https://xsyczb.0rui.cn/uploads/ziti/SourceHanSerif.ttf")',
				success: function(res) {
					console.log('字体加载成功');
					console.log(res);
				}
			})
  });

  onError((err) => {
    console.log('AppOnError:', err);
  });

  onShow(() => {
    // #ifdef APP-PLUS
    // 获取urlSchemes参数
    const args = plus.runtime.arguments;
    if (args) {
    }

    // 获取剪贴板
    uni.getClipboardData({
      success: (res) => {},
    });
    // #endif
  });
</script>

<style lang="scss">
  @import '@/sheep/scss/index.scss';
  page{
    font-family: 'Source Han Serif'!important;
  }
  .noImg{
    width: 0;
    height: 0;
  }
  /*保留两行文本*/
  .text-overflow-2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
 /*保留1行文本*/
 .text-overflow-1 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }

  :deep() {
    .uni-datetime-picker--btn{
      background-color: #9E4242 !important;
      color: #D3AC8B !important;
    }
    .uni-calendar-item__weeks-box .uni-calendar-item--checked {
      background-color: #D3AC8B !important;
      color: #9E4242 !important;
    }
    .uni-calendar-item__weeks-box-text {
      color: #9E4242;
    }
    .uni-calendar-item--before-checked-x {
      background-color: #fff!important;
    }
  }
</style>
