<template>
	<view style="background-color: #190000;min-height: 100vh;">
		<view v-if="!topShow" class="header-normal"
			style="width: 100%;;display: flex;align-items: center;gap: 100rpx;top: 90rpx;height: 80rpx;line-height: 80rpx;position: fixed;z-index: 100;">
			<image class="noImg" src="/static/img/logo.png" style="width: 290rpx;" mode="widthFix"></image>
			<view style="display: flex;align-items: center;gap: 30rpx;">
				<image @click="openUrl('/pages/index/search')" src="/static/img/top1.png"
					style="width: 60rpx;height: 60rpx;"></image>
				<view>
					<button open-type="share" style="height: 60rpx;">
						<image src="/static/img/top2.png" style="width: 60rpx;height: 60rpx;"></image>
					</button>
				</view>
			</view>
		</view>
		<view v-if="topShow" class="header-scrolled"
			style="justify-content: center;text-align: center;background-color: #190000;width: 100%;;display: flex;align-items: center;gap: 100rpx;padding-top: 90rpx;height: 80rpx;line-height: 80rpx;position: fixed;z-index: 100;">
			<image class="noImg" src="/static/img/logo.png" style="width: 290rpx;" mode="widthFix"></image>
		</view>
		<view>
			<swiper class="swiper_s" :circular="true" :autoplay="true" indicator-active-color="#0DAE11"
				indicator-color="#ffffff" :indicator-dots="false">
				<swiper-item v-for="item in bannerList" style="margin: 0 auto;">
					<view style="position: relative;width: 100%;height: 100%;">
						<image class="swiper-image" :src="item.image" mode="scaleToFill" />
					</view>
				</swiper-item>
			</swiper>
		</view>
		<view style="display: flex;justify-content: center;margin-top: 30rpx;"
			v-if="newGoodsList.length > 0 && newGoodsList[0]">
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/left.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
			<view style="letter-spacing:8rpx;margin-right: -8rpx;">
				<text style="color: #FFFFFF;font-size: 36rpx;font-weight: 900;">{{
					newGoodsList[0].name }}</text>
			</view>
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/right.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view style="margin-top: 50rpx;" v-if="newGoodsList.length > 0 && newGoodsList[0] && newGoodsList[0].goods">
			<scroll-view scroll-x="true" class="goods-scroll-container" show-scrollbar="false">
				<view class="goods-list">
					<view class="goodsBg" v-for="item in newGoodsList[0].goods" :key="item"
						@click="openUrl('/pages/goods/index?id=' + item.id)">
						<image :src="item.full_image" style="width: 210rpx;height: 210rpx;object-fit: cover;"></image>
						<view class="text-overflow-2" style="font-weight: 700;font-size: 28rpx;color: #F0DBC5;margin: 20rpx 0rpx;">{{ item.title
						}}</view>
						<view style="font-weight: 700;font-size: 28rpx;color: #FFC07E;">￥{{ item.price[0] }}</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view style="display: flex;justify-content: center;margin-top: 50rpx;"
			v-if="newGoodsList.length > 1 && newGoodsList[1]">
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/left.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
			<view style="letter-spacing:8rpx;margin-right: -8rpx;">
				<text style="color: #FFFFFF;font-size: 36rpx;font-weight: 900;">{{
					newGoodsList[1].name }}</text>
			</view>
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/right.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view style="margin: 50rpx;"
			v-if="newGoodsList.length > 1 && newGoodsList[1] && newGoodsList[1].goods && newGoodsList[1].goods.length > 1">
			<view style="display: flex;">
				<view class="goodsTowBg">
					<view style="display: flex;align-items: center;justify-content: center;">
						<view @click="openUrl('/pages/goods/index?id=' + newGoodsList[1].goods[0].id)">
							<view
								style="margin: 0 auto;text-align: center;width: 240rpx;font-weight: 900;font-size: 28rpx;color: #F0DBC5;">
								{{ newGoodsList[1].goods[0].title }}</view>
							<view
								style="font-size: 28rpx;color: #F0DBC5;border-radius: 10rpx;font-weight: 300;margin: 0 auto;border: 1px solid #F0DBC5;width: 200rpx;height: 65rpx;line-height: 65rpx;text-align: center;margin-top: 30rpx;">
								查看详情</view>
						</view>
					</view>
				</view>
				<view style="width: 345rpx;height: 345rpx;"
					@click="openUrl('/pages/goods/index?id=' + newGoodsList[1].goods[0].id)">
					<image :src="newGoodsList[1].goods[0].full_image"
						style="width: 100%;height: 100%;object-fit: cover;border-radius: 0px 16rpx 0px 0px;">
					</image>
				</view>
			</view>
			<view style="display: flex;">
				<view style="width: 345rpx;height: 345rpx;"
					@click="openUrl('/pages/goods/index?id=' + newGoodsList[1].goods[1].id)">
					<image :src="newGoodsList[1].goods[1].full_image"
						style="width: 100%;height: 100%;object-fit: cover;border-radius: 0px 0rpx 0px 16rpx;">
					</image>
				</view>
				<view class="goodsTowBg2">
					<view style="display: flex;align-items: center;justify-content: center;">
						<view @click="openUrl('/pages/goods/index?id=' + newGoodsList[1].goods[1].id)">
							<view
								style="margin: 0 auto;text-align: center;width: 240rpx;font-weight: 900;font-size: 28rpx;color: #F0DBC5;">
								{{ newGoodsList[1].goods[1].title }}</view>
							<view
								style="font-size: 28rpx;color: #F0DBC5;border-radius: 10rpx;font-weight: 300;margin: 0 auto;border: 1px solid #F0DBC5;width: 200rpx;height: 65rpx;line-height: 65rpx;text-align: center;margin-top: 30rpx;">
								查看详情</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view style="display: flex;justify-content: center;margin-top: 50rpx;" v-if="newTypeList && newTypeList.name">
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/left.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
			<view style="letter-spacing:8rpx;margin-right: -8rpx;">
				<text style="color: #FFFFFF;font-size: 36rpx;font-weight: 900">{{ newTypeList.name
				}}</text>
			</view>
			<view style="margin-top: 30rpx;">
				<image class="noImg" src="/static/img/right.png" style="width: 160rpx;" mode="widthFix"></image>
			</view>
		</view>
		<view class="grid-container" v-if="newTypeList && newTypeList.gory && newTypeList.gory.length > 0">
			<template v-for="(item, index) in newTypeList.gory" :key="index">
				<view class="grid-item" style="text-align: center;">
					<image :src="item.image" style="height: 150rpx;width: 150rpx;object-fit: cover;"></image>
					<view style="font-weight: 700;font-size: 28rpx;color: #F0DBC5;margin-top: 20rpx;">{{ item.name }}
					</view>
				</view>
				<!-- 分隔线：只在不是每行最后一个且不是最后一个商品时显示 -->
				<view v-if="(index + 1) % 3 !== 0 && index < 5" class="grid-divider"></view>
			</template>
		</view>
		<view style="margin-top: 30rpx;">
			<view style="position: relative;" v-if="newGoodsList.length > 2 && newGoodsList[2]">
				<image class="noImg" src="https://xsyczb.0rui.cn/my_img/xilie1.png" style="width: 100%;display: block;"
					mode="widthFix">
				</image>
				<view
					style="z-index: 10;font-size: 36rpx;color: #FFFFFF;font-weight: 900;position: absolute;width: 100%;text-align: center;bottom: 90rpx;">
					{{ newGoodsList[2].name }}</view>
			</view>
		</view>
		<view style="background-color: #F7F7F7;padding: 30rpx;"
			v-if="newGoodsList.length > 2 && newGoodsList[2] && newGoodsList[2].goods">
			<scroll-view scroll-x="true" class="product-scroll-container" show-scrollbar="false">
				<view class="product-grid-wrapper">
					<view v-for="item in newGoodsList[2].goods" :key="item" class="product-grid-item"
						@click="openUrl('/pages/goods/index?id=' + item.id)">
						<view>
							<image :src="item.full_image"
								style="width: 160rpx;height: 160rpx;object-fit: cover;border-radius: 16rpx;"></image>
						</view>
						<view style="flex: 1;padding-left: 20rpx;min-width: 0;overflow: hidden;">
							<view class="text-overflow-2"
								style="font-weight: 700;font-size: 28rpx;color: #323232;line-height: 1.3;word-wrap: break-word;word-break: break-all;white-space: normal;max-height: 76rpx;overflow: hidden;">
								{{ item.title }}</view>
							<view
								style="display: flex;justify-content: space-between;align-items: center;margin-top: 20rpx;">
								<view style="font-weight: 700;color: #9E4242;font-size: 28rpx;">￥{{ item.price[0] }}
								</view>
								<view style="font-size: 24rpx;font-weight: 700;color: #999999;">{{ item.views }}已看
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<view @click="openUrl('/pages/goods/list?categoryId=' + newGoodsList[2].id)"
				style="font-size: 28rpx;color: #323232;border: 1rpx solid #323232;width: 220rpx;height: 60rpx;text-align: center;line-height: 60rpx;margin: 0 auto;margin-top: 30rpx;"
				v-if="newGoodsList.length > 2 && newGoodsList[2] && newGoodsList[2].id">
				查看更多</view>
		</view>
		<view style="margin-top: 50rpx;">
			<view style="position: relative;" v-if="newGoodsList.length > 3 && newGoodsList[3]">
				<image class="noImg" src="https://xsyczb.0rui.cn/my_img/xilie.png" style="width: 100%;display: block;"
					mode="widthFix">
				</image>
				<view
					style="z-index: 10;font-size: 36rpx;color: #FFFFFF;font-weight: 900;position: absolute;width: 100%;text-align: center;bottom: 90rpx;">
					{{ newGoodsList[3].name }}</view>
			</view>
		</view>
		<view style="background: #F7F7F7;padding: 30rpx;"
			v-if="newGoodsList.length > 3 && newGoodsList[3] && newGoodsList[3].goods">
			<view style="display: grid;grid-template-columns: 1fr 1fr;gap: 30rpx;">
				<view v-for="item in newGoodsList[3].goods" :key="item"
					@click="openUrl('/pages/goods/index?id=' + item.id)"
					style="background-color: #FFFFFF;border-radius: 16rpx;padding: 20rpx;">
					<view>
						<image :src="item.full_image"
							style="width: 100%;height: 310rpx;object-fit: cover;border-radius: 16rpx;"></image>
					</view>
					<view style="margin-top: 20rpx;text-align: center;">
						<view class="text-overflow-2" style="font-weight: 700;font-size: 28rpx;color: #323232;">
							{{ item.title }}
						</view>
					</view>
					<view
						style="font-weight: 700;font-size: 28rpx;color: #9E4242;margin-top: 20rpx;text-align: center;">
						￥{{ item.price[0] }}
					</view>
				</view>
			</view>
			<view @click="openUrl('/pages/goods/list?categoryId=' + newGoodsList[3].id)"
				style="font-size: 28rpx;color: #323232;border: 1rpx solid #323232;width: 220rpx;height: 60rpx;text-align: center;line-height: 60rpx;margin: 0 auto;margin-top: 30rpx;"
				v-if="newGoodsList.length > 3 && newGoodsList[3] && newGoodsList[3].id">
				查看更多</view>
		</view>
		<view>
			<image class="noImg" src="https://xsyczb.0rui.cn/my_img/about.png" style="width: 100%;display: block;"
				mode="widthFix">
			</image>
		</view>
		<!-- 首页弹窗 -->
		<!-- <su-popup :show="registerShow" type="top" :isMaskClick="false" backgroundColor="none"
			@close="registerShow = false" @open="registerShow = true">
			<view style="position: absolute;right: 90rpx;top: 390rpx;" @click="registerShow = false">
				<image src="/static/img/x.png" style="width: 60rpx;height: 60rpx;"></image>
			</view>
			<view style="text-align: center;">
				<image class="noImg" src="https://xsyczb.0rui.cn/my_img/pop.png" style="width: 690rpx;" mode="widthFix">
				</image>
			</view>
		</su-popup> -->
		<su-popup :show="registerShow == true" type="top" round="10" :isMaskClick="false" backgroundColor="none"
			@close="registerShow = false" @open="registerShow = true">
			<view class="popup" @click="newReceiveCoupon()">
				<view class="regListBox" v-for="(item, index) in registerList" :key="index">
					<view class="reItem">
						<view>
							<view style="font-size: 36rpx;font-weight: 900;color: #fff;line-height: 40rpx;">
								{{ item.amount }}元
							</view>
							<view style="font-size: 24rpx;color: #fff;line-height: 34rpx;margin-top: 10rpx;">
								{{ item.amount_text }}
							</view>
						</view>
						<view
							style="font-size: 26rpx;font-weight: bold;color: #fff;line-height: 36rpx;margin-left: 10rpx;">
							{{ item.type_text }}
						</view>
					</view>
				</view>
				<view @click.stop="registerShow = false" style="margin: 0 auto;position: absolute;bottom: -90rpx;">
					<image style="width: 50rpx;height: 50rpx;"
						src="https://xsyczb.0rui.cn/my_img/registerCancel.png" mode="aspectFill"
						></image>
				</view>
			</view>
		</su-popup>
		<s-tabbar path="/pages/index/index" />
	</view>
</template>

<script setup>
import {
	computed,
	ref,
	reactive,
} from 'vue';
import {
	onLoad,
	onShow,
	onPageScroll,
	onPullDownRefresh,
	onShareAppMessage
} from '@dcloudio/uni-app';
import sheep from '@/sheep';
import $share from '@/sheep/platform/share';
import {
	consignee,
	mobile,
	address,
	region
} from '@/sheep/validate/form';
import { showShareModal } from '@/sheep/hooks/useModal';
const topShow = ref(false);


// 为数据提供安全的默认结构，防止访问 undefined 属性
const newGoodsList = ref([
	{ name: '', goods: [], id: '' },
	{ name: '', goods: [{ title: '', full_image: '', id: '' }, { title: '', full_image: '', id: '' }], id: '' },
	{ name: '', goods: [], id: '' },
	{ name: '', goods: [], id: '' }
]);
const newTypeList = ref({ name: '', gory: [] });
function getType() {
	sheep.$api.rent.getHomeNewGoods().then((res) => {
		// 确保返回的数据结构正确，防止访问 undefined 属性
		if (res && res.data && Array.isArray(res.data)) {
			newGoodsList.value = res.data;
		} else {
			console.warn('获取商品列表数据格式异常:', res);
		}
	}).catch((error) => {
		console.error('获取商品列表失败:', error);
		// 保持默认的安全结构，不修改 newGoodsList
	});
};
function getTypeList() {
	console.log(123);
	sheep.$api.rent.getHomeTypeList().then((res) => {
		// 确保返回的数据结构正确，防止访问 undefined 属性
		if (res && res.data && typeof res.data === 'object') {
			newTypeList.value = res.data;
		} else {
			console.warn('获取分类列表数据格式异常:', res);
		}
	}).catch((error) => {
		console.error('获取分类列表失败:', error);
		// 保持默认的安全结构，不修改 newTypeList
	});
};

function getBanner() {
	// const res = sheep.$api.home.homeBanner({});
	// console.log('banner', res.data);
	// if (res.code == 1) {
	// 	bannerList.value = res.data.list
	// 	console.log('bannerList', bannerList.value);
	// }

	sheep.$api.home.homeBanner().then((res) => {
		console.log('banner', res.data);
		bannerList.value = res.data.list
	}).catch((error) => {
		console.error('获取首页轮播图失败:', error);
		// 保持默认的安全结构，不修改 newTypeList
	});
}
const isLogin = computed(() => sheep.$store('user').isLogin);
const registerShow = ref(false); //注册获取优惠券弹框
const registerList = ref([]);
const registerNum = ref(0);

function openUrl(url) {
	uni.navigateTo({
		url: url
	})
}


onLoad((options) => {
	//getAreaCity();
	getType();
	getTypeList();
	getBanner();

	// #ifdef MP
	// 小程序识别二维码
	if (options.scene) {
		const sceneParams = decodeURIComponent(options.scene).split('=');
		options[sceneParams[0]] = sceneParams[1];
	}
	// #endif

	// 预览模板
	if (options.templateId) {
		sheep.$store('app').init(options.templateId);
	}

	// 解析分享信息
	if (options.spm) {
		$share.decryptSpm(options.spm);
	}

	// 进入指定页面(完整页面路径)
	if (options.page) {
		sheep.$router.go(decodeURIComponent(options.page));
	}
});

onShow(() => {
	getScoreInfo();

})
function getScoreInfo() {
	sheep.$api.coupon.registerCoupon().then((res) => {
		if (res.code == 1) {
			console.log('res-registerCoupon:', res);
			registerList.value = res.data.data;
			registerNum.value = res.data.total
			if (registerNum.value != 0) {
				registerShow.value = true;
			} else {
				registerShow.value = false;
			}
			console.log('新用户', registerShow.value);
		} else {
			console.log('非新用户');
			sheep.$helper.toast(res.msg);
		}
	})
}

function newReceiveCoupon() {
	if (isLogin.value == true) {
		sheep.$api.coupon.registerCouponReceive().then((res) => {
			if (res.code == 1) {
				console.log('registerCouponReceive:', res);
				sheep.$helper.toast(res.msg);
				registerShow.value = false;
				setTimeout(function () {
					uni.switchTab({
						url: '/pages/index/category'
					})
				}, 500);
			} else {
				sheep.$helper.toast(res.msg);
			}
		})
	} else {
		sheep.$helper.toast('请先登录');
		// uni.showToast({
		//  title: '请先登录',
		//  icon: 'none',
		//  duration: 2000
		// })

		setTimeout(function () {
			uni.switchTab({
				url: '/pages/index/user'
			})
		}, 1000);

	}


}

//轮播图跳转
function swiperJump(item) {
	console.log('轮播图跳转事件：', item.url);
	const tabBarPages = [
		'/pages/index/index',
		'/pages/index/category',
		'/pages/index/user'
	];
	console.log('tabbarsList：', tabBarPages);
	if (item.type == "in") {
		console.log('跳进内页');
		if (tabBarPages.includes(item.url)) {
			console.log('导航页');
			uni.switchTab({
				url: item.url,
			})
		} else {
			uni.navigateTo({
				url: item.url,
			})
		}
	}
}

onPageScroll((e) => {
	// 设置滚动阈值为200rpx，超过此距离显示顶部导航
	const scrollThreshold = 200;

	if (e.scrollTop > scrollThreshold) {
		topShow.value = true;
	} else {
		topShow.value = false;
	}
});

const bannerList = ref([])


const rentList = ref([]);
//招租列表

// 隐藏原生tabBar
uni.hideTabBar({
	fail: () => { },
});

// 微信小程序分享功能
// #ifdef MP-WEIXIN
onShareAppMessage(() => {

});
// #endif
</script>

<style lang="scss" scoped>
/* 对于没有安全区域的设备，设置默认值 */
@supports not (padding-bottom: env(safe-area-inset-bottom)) {
	.scrollBox {
		padding-bottom: 0;
	}
}

.swiper-image {
	width: 100%;
	height: 100%;
	object-fit: contain;
}

.swiper_s {
	height: 1200rpx;
}

/* 横向滚动容器样式 */
.goods-scroll-container {
	width: 100%;
	height: 400rpx;
	white-space: nowrap;
}

.goods-list {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	padding: 0 30rpx;
	gap: 30rpx;
}

/* Grid布局样式 */
.grid-container {
	display: grid;
	grid-template-columns: 1fr auto 1fr auto 1fr;
	gap: 0;
	align-items: center;
	padding: 0 30rpx;
	margin-top: 30rpx;
}

.grid-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	margin: 30rpx 0rpx;
}

.grid-divider {
	justify-self: center;
	align-self: center;
	width: 1rpx;
	height: 80rpx;
	background: rgb(211, 172, 139, 0.3);
}

/* 商品横向滚动网格布局样式 */
.product-scroll-container {
	width: 100%;
	height: 580rpx;
	white-space: nowrap;
}

.product-grid-wrapper {
	display: grid;
	grid-template-rows: repeat(3, 180rpx);
	grid-template-columns: 550rpx 550rpx;
	gap: 20rpx;
	grid-auto-flow: row;
	height: 100%;
	padding-right: 30rpx;
	width: max-content;
}

.product-grid-item {
	flex-shrink: 0;
	box-sizing: border-box;
	width: 550rpx;
	height: 180rpx;
	border-radius: 16rpx;
	padding: 10rpx 30rpx 10rpx 10rpx;
	display: flex;
	align-items: center;
	background-color: #ffffff;
}

.goodsBg {
	background-image: url("https://xsyczb.0rui.cn/my_img/goodsBg.png");
	background-size: 100% 100%;
	text-align: center;
	width: 240rpx;
	height: 360rpx;
	padding: 10rpx 15rpx;
	flex-shrink: 0;
	/* 防止商品卡片被压缩 */
}

.goodsTowBg {
	background-image: url("https://xsyczb.0rui.cn/my_img/left_top.png");
	background-size: 100% 100%;
	width: 345rpx;
	height: 345rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

.goodsTowBg2 {
	background-image: url("https://xsyczb.0rui.cn/my_img/bottom_right.png");
	background-size: 100% 100%;
	width: 345rpx;
	height: 345rpx;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 头部导航渐显动画样式 */
.header-normal {
	opacity: 1;
	transition: opacity 0.5s ease-in-out;
}

.header-scrolled {
	opacity: 1;
	transition: opacity 0.5s ease-in-out;
}

/* 当topShow状态改变时的动画效果 */
.header-normal.fade-out {
	opacity: 0;
}

.header-scrolled.fade-in {
	opacity: 1;
}

.popup {
	background-image: url('https://xsyczb.0rui.cn/my_img/registerBack.png');
	background-size: 100% 100%;
	width: 750rpx;
	height: 1076rpx;
	display: flex;
	// padding: 100rpx 73rpx 0rpx 73rpx;
	flex-direction: column;
	align-items: center;
	justify-content: flex-start;
	position: relative;
}

.regListBox {
	background-image: url('https://xsyczb.0rui.cn/my_img/youhui.png');
	background-size: 100% 100%;
	width: 490rpx;
	height: 137rpx;
	position: absolute;
    bottom: 290rpx;
}
.reItem{
	display: flex;
    justify-content: center;
    align-items: center;
    height: 137rpx;
	gap: 140rpx;
}
/* 去除button默认样式 */
button {
	background: none;
	border: none;
	padding: 0;
	margin: 0;
	outline: none;
	box-shadow: none;
	border-radius: 0;
	font-size: inherit;
	color: inherit;
	line-height: inherit;
}

button::after {
	border: none;
}
</style>